{"expo": {"name": "native-ui", "slug": "native-ui", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.lymandumas.nativeui", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-web-browser", "expo-secure-store", "expo-localization", "expo-iap", "expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "75cef86e-0c74-4db6-9d21-ce65c72566ba"}}}}